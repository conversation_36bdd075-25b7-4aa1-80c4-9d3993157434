from typing import List, Optional
from decimal import Decimal

class IndicatorSet:
    def __init__(self, ema_fast: Optional[Decimal], ema_mid: Optional[Decimal], ema_slow: Optional[Decimal], bb_lower: Optional[Decimal]):
        self.ema_fast = ema_fast
        self.ema_mid = ema_mid
        self.ema_slow = ema_slow
        self.bb_lower = bb_lower

# Simple EMA & Bollinger lower calculations without external deps

def _ema(values: List[Decimal], period: int) -> Optional[Decimal]:
    if len(values) < period or period <= 0:
        return None
    k = Decimal("2") / Decimal(period + 1)
    ema_val = values[0]
    for v in values[1:]:
        ema_val = v * k + ema_val * (Decimal("1") - k)
    return ema_val

def _sma(values: List[Decimal], period: int) -> Optional[Decimal]:
    if len(values) < period:
        return None
    window = values[-period:]
    return sum(window) / Decimal(period)

def _std(values: List[Decimal], period: int) -> Optional[Decimal]:
    if len(values) < period:
        return None
    window = values[-period:]
    mean = sum(window) / Decimal(period)
    var = sum((x - mean) ** 2 for x in window) / Decimal(period)
    # naive sqrt via power 0.5
    return var.sqrt() if hasattr(var, 'sqrt') else Decimal(var) ** Decimal("0.5")

def compute_indicators(closes: List[Decimal], ema_periods: tuple[int,int,int], bb_period: int, bb_std: int) -> IndicatorSet:
    e_fast = _ema(closes, ema_periods[0])
    e_mid = _ema(closes, ema_periods[1])
    e_slow = _ema(closes, ema_periods[2])
    mid = _sma(closes, bb_period)
    sd = _std(closes, bb_period)
    bb_lower = None
    if mid is not None and sd is not None:
        bb_lower = mid - Decimal(bb_std) * sd
    return IndicatorSet(e_fast, e_mid, e_slow, bb_lower)

