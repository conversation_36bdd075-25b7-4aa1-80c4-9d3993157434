from typing import Dict, List, Optional
from decimal import Decimal
from dataclasses import dataclass

from .signal_engine import compute_indicators

class DCAType:
    BB_LOWER = "BB_LOWER"
    EMA_89 = "EMA_89"
    EMA_34 = "EMA_34"
    EMA_120 = "EMA_120"

@dataclass
class DCATarget:
    price: Decimal
    amount_usd: Decimal
    dca_type: str

@dataclass
class DCAConfig:
    enabled: bool
    strategies: Dict[str, Dict]
    price_update_tolerance: Decimal


def compute_dca_targets(candles_by_tf: Dict[str, List[Dict]], ema_periods: tuple[int,int,int], bb_period: int, bb_std: int, cfg: DCAConfig) -> List[DCATarget]:
    if not cfg.enabled:
        return []
    out: List[DCATarget] = []
    for dca_type, dconf in cfg.strategies.items():
        if not dconf.get("enabled"):
            continue
        tf = dconf.get("timeframe")
        arr = candles_by_tf.get(tf)
        if not arr or len(arr) < max(ema_periods[2], bb_period + 2):
            continue
        closes = [x["close"] for x in arr]
        indi = compute_indicators(closes, ema_periods, bb_period, bb_std)
        price = None
        if dca_type == DCAType.BB_LOWER:
            price = indi.bb_lower
        elif dca_type == DCAType.EMA_89:
            price = indi.ema_mid
        elif dca_type == DCAType.EMA_34:
            price = indi.ema_fast
        elif dca_type == DCAType.EMA_120:
            price = indi.ema_slow
        if price is None or price <= 0:
            continue
        amt = Decimal(str(dconf.get("amount", 0)))
        if amt <= 0:
            continue
        out.append(DCATarget(price=price, amount_usd=amt, dca_type=dca_type))
    return out

