# This script is intended to run inside a Hummingbot container
# It uses the ScriptStrategyBase API. Import paths and enums are based on
# Hummingbot's public interfaces.

from typing import Dict, List, Optional
from decimal import Decimal
from datetime import datetime, timedelta

try:
    from hummingbot.strategy_v2.script_strategy_base import ScriptStrategyBase as HBStrategyBase
    STRATEGY_API_VERSION = 2
except Exception:
    try:
        from hummingbot.strategy.script_strategy_base import ScriptStrategyBase as HBStrategyBase
        STRATEGY_API_VERSION = 1
    except Exception:
        HBStrategyBase = object  # type: ignore
        STRATEGY_API_VERSION = 0
# Common enums (fallback if not available)
try:
    from hummingbot.core.data_type.common import TradeType, OrderType, PositionAction
except Exception:
    class TradeType: BUY = 1; SELL = 2  # type: ignore
    class OrderType: LIMIT = 1; MARKET = 2  # type: ignore
    class PositionAction: OPEN = 1; CLOSE = 2  # type: ignore

from .signal_engine import IndicatorSet, compute_indicators
from .dca_manager import DCAConfig, compute_dca_targets, DCAType
from .risk import SizingConfig, RiskLimits, size_position_usd


class PlannedPairStrategy(HBStrategyBase):
    # Configure your markets via HB client or here
    markets = {"bybit_perpetual": ["BTC-USDT"]}

    def __init__(self):
        super().__init__()
        # Load parameters (can be mapped from YAML via self._config_map in HB)
        self.primary_timeframe = "15m"
        self.tf_list = ["15m", "1h", "4h"]
        self.ema_periods = (34, 89, 120)
        self.bb_period = 20
        self.bb_std = 2
        self.signal_cooldown = timedelta(minutes=3)
        self._last_signal_at: Optional[datetime] = None

        # Trading direction and amounts
        self.direction = "LONG"  # or "SHORT"
        self.leverage = Decimal("10")

        # Risk/sizing config (USD based sizing)
        self.sizing = SizingConfig(min_usd=Decimal("10"), max_usd=Decimal("500"))
        self.risk = RiskLimits(max_daily_trades=10, daily_loss_limit_pct=Decimal("10"))

        # DCA configuration
        self.dca_cfg = DCAConfig(
            enabled=True,
            strategies={
                DCAType.BB_LOWER: {"timeframe": "15m", "amount": Decimal("50"), "enabled": True},
                DCAType.EMA_89: {"timeframe": "1h", "amount": Decimal("50"), "enabled": False}
            },
            price_update_tolerance=Decimal("0.001")
        )

    # Candle subscription
    def on_stop(self):
        pass

    def on_tick(self):
        try:
            connector = list(self.markets.keys())[0]
            trading_pair = self.markets[connector][0]

            # Acquire candles for all needed timeframes
            candles: Dict[str, List[Dict]] = {}
            for tf in self.tf_list:
                c = self.candles_df(connector, trading_pair, tf)
                if c is None or len(c) < max(120, self.bb_period + 2):
                    return
                # Convert to list of dicts with 'close'
                closes = [Decimal(str(x)) for x in c['close'].tolist()]  # type: ignore
                candles[tf] = [{"close": v} for v in closes]

            # Compute indicators on primary timeframe
            primary = candles[self.primary_timeframe]
            indicators: IndicatorSet = compute_indicators(
                [x["close"] for x in primary], self.ema_periods, self.bb_period, self.bb_std
            )

            # Cooldown
            now = datetime.utcnow()
            if self._last_signal_at and now - self._last_signal_at < self.signal_cooldown:
                self.logger().debug("Signal on cooldown")
                return

            # Planned pair entry at EMA_34 (stink bid style)
            ema34 = indicators.ema_fast
            if ema34 is None:
                return

            side = TradeType.BUY if self.direction.upper() == "LONG" else TradeType.SELL

            # Determine USD sizing (paper: use available balance or fixed amount)
            usd_amount = size_position_usd(self, connector, trading_pair, self.leverage, self.sizing)
            if usd_amount is None:
                return

            # Convert USD -> base amount by price (approx using last price)
            price = self.get_price(connector, trading_pair, side == TradeType.BUY)
            if price is None or price <= Decimal("0"):
                return
            contracts = (usd_amount / price).quantize(Decimal("0.000001"))
            if contracts <= Decimal("0"):
                return

            # Check existing entry orders and cancel/keep by tolerance
            tol = self.dca_cfg.price_update_tolerance
            kept = self._cancel_or_keep_existing(connector, trading_pair, side, ema34, tol)
            if kept:
                return

            # Place limit entry at EMA_34
            self.place_order(connector, trading_pair, side, OrderType.LIMIT, contracts, ema34, PositionAction.OPEN)
            self._last_signal_at = now

            # Process DCA targets (create/update per strategy)
            targets = compute_dca_targets(candles, self.ema_periods, self.bb_period, self.bb_std, self.dca_cfg)
            for t in targets:
                self._ensure_dca_order(connector, trading_pair, side, t.price, t.amount_usd, tol)

        except Exception as e:
            self.logger().error(f"Tick error: {e}")

    # --- Helpers
    def _cancel_or_keep_existing(self, connector: str, pair: str, side: TradeType, new_price: Decimal, tol: Decimal) -> bool:
        try:
            open_orders = self.get_active_orders(connector=connector, trading_pair=pair)
            for o in open_orders:
                if o.is_buy != (side == TradeType.BUY):
                    continue
                if o.order_type != OrderType.LIMIT:
                    continue
                if o.is_open:
                    old = Decimal(str(o.price))
                    diff = abs(old - new_price) / new_price
                    if diff <= tol:
                        self.logger().info(f"Keep existing entry {o.client_order_id} @ {old}")
                        return True
                    else:
                        self.cancel(connector, pair, o.client_order_id)
                        self.logger().info(f"Cancel entry {o.client_order_id} old={old} new={new_price}")
            return False
        except Exception as e:
            self.logger().warning(f"cancel_or_keep_existing err: {e}")
            return False

    def _ensure_dca_order(self, connector: str, pair: str, side: TradeType, price: Decimal, usd_amount: Decimal, tol: Decimal):
        if usd_amount <= 0 or price <= 0:
            return
        base_amount = (usd_amount / price).quantize(Decimal("0.000001"))
        if base_amount <= 0:
            return
        # Update if similar exists
        try:
            open_orders = self.get_active_orders(connector=connector, trading_pair=pair)
            for o in open_orders:
                if o.is_buy != (side == TradeType.BUY):
                    continue
                if o.order_type != OrderType.LIMIT:
                    continue
                old = Decimal(str(o.price))
                diff = abs(old - price) / price
                if diff <= tol:
                    return  # already good
                # else replace
                self.cancel(connector, pair, o.client_order_id)
        except Exception:
            pass
        self.place_order(connector, pair, side, OrderType.LIMIT, base_amount, price, PositionAction.OPEN)

    def place_order(self, connector: str, pair: str, side: TradeType, order_type: OrderType, amount: Decimal, price: Decimal, action: PositionAction):
        if side == TradeType.BUY:
            self.buy(connector_name=connector, trading_pair=pair, amount=amount, order_type=order_type, price=price, position_action=action)
        else:
            self.sell(connector_name=connector, trading_pair=pair, amount=amount, order_type=order_type, price=price, position_action=action)

