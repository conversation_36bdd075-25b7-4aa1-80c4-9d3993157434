from decimal import Decimal
from dataclasses import dataclass
from typing import Optional

@dataclass
class SizingConfig:
    min_usd: Decimal
    max_usd: Decimal

@dataclass
class RiskLimits:
    max_daily_trades: int
    daily_loss_limit_pct: Decimal


def size_position_usd(strategy, connector: str, pair: str, leverage: Decimal, sizing: SizingConfig) -> Optional[Decimal]:
    """Very simple USD sizing: clamp between min/max. Could be improved by reading
    balance info via strategy.connector.
    """
    # For now, return middle of min/max as placeholder
    usd = (sizing.min_usd + sizing.max_usd) / Decimal("2")
    if usd < sizing.min_usd:
        usd = sizing.min_usd
    if usd > sizing.max_usd:
        usd = sizing.max_usd
    return usd

