import unittest
from decimal import Decimal
from scripts.signal_engine import compute_indicators

class TestSignalEngine(unittest.TestCase):
    def test_indicators(self):
        closes = [Decimal("100") + Decimal(i) for i in range(200)]
        indi = compute_indicators(closes, (34, 89, 120), 20, 2)
        self.assertIsNotNone(indi.ema_fast)
        self.assertIsNotNone(indi.ema_mid)
        self.assertIsNotNone(indi.ema_slow)
        self.assertIsNotNone(indi.bb_lower)

if __name__ == '__main__':
    unittest.main()

