import unittest
from decimal import Decimal
from scripts.dca_manager import DCAConfig, compute_dca_targets, DCAType

class TestDCAManager(unittest.TestCase):
    def test_dca_targets(self):
        # build candles with increasing closes
        closes = [Decimal("100") + Decimal(i) for i in range(200)]
        candles = {"15m": [{"close": c} for c in closes], "1h": [{"close": c} for c in closes], "4h": [{"close": c} for c in closes]}
        cfg = DCAConfig(enabled=True, strategies={DCAType.BB_LOWER: {"timeframe": "15m", "amount": Decimal("50"), "enabled": True}}, price_update_tolerance=Decimal("0.001"))
        targets = compute_dca_targets(candles, (34,89,120), 20, 2, cfg)
        self.assertTrue(len(targets) >= 1)
        self.assertGreater(targets[0].price, 0)
        self.assertEqual(targets[0].amount_usd, Decimal("50"))

if __name__ == '__main__':
    unittest.main()

