# Trade Bot (Hummingbot-based)

This repo hosts strategy scripts and configs to run with Hummingbot.

Quick start (Docker):
1. docker pull hummingbot/hummingbot:latest
2. docker run -it --name hb \
   -v $(pwd)/conf:/conf -v $(pwd)/logs:/logs -v $(pwd)/scripts:/scripts \
   hummingbot/hummingbot:latest
3. In HB, set your connector (e.g., bybit_perpetual testnet), load script:
   start script scripts/planned_pair_strategy.py

Adjust markets and parameters inside the script or via HB config.

